import dill
# ps = dill.load(open('promptlabels_truly_fixed.pkl', 'rb'))
ps = dill.load(open('production_dataset.pkl', 'rb'))
print(len(ps))

print(len(ps[0]))
for p in ps[0]:
    print(p)

# find ids of ps that has "artist:ciloranko" or "ciloranko"
ids = [i for i, p in enumerate(ps) if any("ciloranko" in t[0] for t in p[1])]
print(len(ids))
idx = ids[0]
print(idx)
print(ps[idx][0])
print(ps[idx][1])

# find filename that contains "ComfyUI_00212_.png"
# idx = [i for i, p in enumerate(ps) if "2025-06-22\\ComfyUI_00049_.png" in p[0]]
idx = [i for i, p in enumerate(ps) if "2024-12-17\\ComfyUI_00099_.png" in p[0]]
print(len(idx))
idx = idx[0]
print(idx)
print(ps[idx][0])
print(ps[idx][1])





















